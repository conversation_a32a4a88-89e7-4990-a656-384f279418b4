import type { SpreadsheetSkeleton, UniverRenderingContext } from '@univerjs/preset-sheets-core'
import type { IScale } from '@univerjs/presets'
import {
  DEFAULT_FONTFACE_PLANE,
  getColor,
  MIDDLE_CELL_POS_MAGIC_NUMBER,
  SheetExtension,
} from '@univerjs/preset-sheets-core'

const UNIQUE_KEY = 'MainCustomExtension'

// 自定义筛选图标
const FILTER_ICONS = {
  normal: '⬇️',      // 普通筛选图标
  filtered: '🔻'     // 已筛选状态的图标
}

export default class MainCustomExtension extends SheetExtension {
  uKey = UNIQUE_KEY

  get zIndex() {
    return 60
  }

  draw(ctx: UniverRenderingContext, _parentScale: IScale, spreadsheetSkeleton: SpreadsheetSkeleton) {
    const { rowColumnSegment } = spreadsheetSkeleton
    const { startRow, endRow, startColumn, endColumn } = rowColumnSegment

    if (!spreadsheetSkeleton) {
      return
    }

    const { rowHeightAccumulation, columnWidthAccumulation } = spreadsheetSkeleton

    if (!rowHeightAccumulation || !columnWidthAccumulation) {
      return
    }

    // 获取筛选器状态
    const filterStatus = this.getFilterStatus()
    if (!filterStatus.hasFilter) {
      return
    }

    let preRowPosition = 0

    for (let r = startRow - 1; r <= endRow; r++) {
      // 只处理表头行
      if (r !== 0 || r < 0 || r > rowHeightAccumulation.length - 1) {
        continue
      }

      const rowEndPosition = rowHeightAccumulation[r]
      if (preRowPosition === rowEndPosition) {
        continue
      }

      let preColumnPosition = 0

      for (let c = startColumn - 1; c <= endColumn; c++) {
        if (c < 0 || c > columnWidthAccumulation.length - 1) {
          continue
        }

        const columnEndPosition = columnWidthAccumulation[c]
        if (preColumnPosition === columnEndPosition) {
          continue
        }

        const middleCellPosX = preColumnPosition + (columnEndPosition - preColumnPosition) / 2
        const middleCellPosY = preRowPosition + (rowEndPosition - preRowPosition) / 2

        // 绘制筛选图标
        this.drawFilterIcon(ctx, middleCellPosX, middleCellPosY, columnEndPosition - preColumnPosition, filterStatus)

        preColumnPosition = columnEndPosition
      }

      preRowPosition = rowEndPosition
    }
  }

  /**
   * 获取筛选器状态
   */
  private getFilterStatus() {
    try {
      if (typeof window !== 'undefined' && (window as any).jobTableUniver) {
        return (window as any).jobTableUniver.getFilterStatus()
      }
    } catch (error) {
      // 忽略错误
    }
    return { hasFilter: false, filteredOutRows: [] }
  }

  /**
   * 绘制筛选图标
   */
  private drawFilterIcon(
    ctx: UniverRenderingContext,
    centerX: number,
    centerY: number,
    columnWidth: number,
    filterStatus: any
  ) {
    ctx.save()

    // 设置筛选图标的位置（在单元格右侧）
    const iconX = centerX + columnWidth / 2 - 15
    const iconY = centerY

    // 根据筛选状态选择图标
    const icon = filterStatus.filteredOutRows && filterStatus.filteredOutRows.length > 0
      ? FILTER_ICONS.filtered
      : FILTER_ICONS.normal

    // 设置图标样式并绘制
    ctx.fillStyle = getColor([100, 100, 100])!
    ctx.font = `12px ${DEFAULT_FONTFACE_PLANE}`
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(icon, iconX, iconY + MIDDLE_CELL_POS_MAGIC_NUMBER)

    ctx.restore()
  }
}
